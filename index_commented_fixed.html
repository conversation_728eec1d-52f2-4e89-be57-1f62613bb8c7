<!DOCTYPE html>
<!-- 
DOCTYPE声明：
- DOCTYPE = Document Type Declaration（文档类型声明）
- html = 告诉浏览器这是HTML5文档
- 这必须是HTML文档的第一行，不能有任何内容在它之前
- 它不是HTML标签，而是一个指令，告诉浏览器使用HTML5标准来解析这个页面
-->

<html lang="zh">
<!-- 
html标签：
- html = 这是根元素，包含整个网页的所有内容
- lang = language属性，告诉浏览器和搜索引擎这个页面的主要语言
- "zh" = 中文的语言代码，帮助屏幕阅读器正确发音
- 开始标签 <html> 和结束标签 </html> 必须成对出现
-->

<head>
<!-- 
head标签：
- head = 文档头部，包含不直接显示在页面上的元数据
- 这些信息是给浏览器、搜索引擎和其他程序使用的
- 用户在网页上看不到head里的内容（除了title会显示在浏览器标签页上）
-->

    <!-- meta标签（字符编码）：
    - meta = metadata（元数据），提供关于HTML文档的信息
    - charset = character set（字符集）属性
    - "UTF-8" = 一种Unicode编码方式，能显示世界上几乎所有的文字和符号
    - 这告诉浏览器用UTF-8编码来读取这个HTML文件
    - 如果没有这个，中文可能显示为乱码
    - 这是自闭合标签，不需要结束标签
    -->
    <meta charset="UTF-8">
    
    <!-- meta标签（视口设置）：
    - name="viewport" = 这是一个视口（viewport）设置
    - content = 内容属性，包含具体的设置值
    - width=device-width = 让页面宽度等于设备屏幕宽度
    - initial-scale=1.0 = 初始缩放比例为100%（不缩放）
    - 这让网页能在手机、平板等不同设备上正常显示（响应式设计的基础）
    -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    
    <!-- meta标签（页面描述）：
    - name="description" = 这是页面描述
    - content = 描述内容，搜索引擎会在搜索结果中显示这个描述
    - 这个描述帮助SEO（搜索引擎优化），让搜索引擎更好地理解你的网页
    -->
    <meta name="description" content="张三的个人作品集 - 前端开发工程师">
    
    <!-- title标签：
    - title = 网页标题
    - 这个文字会显示在：
      1. 浏览器标签页上
      2. 搜索引擎结果中作为链接文字
      3. 收藏夹中的名称
    - 对SEO非常重要
    -->
    <title>张三 - 前端开发工程师</title>
    
    <!-- style标签：
    - style = 样式标签，里面写CSS代码
    - CSS = Cascading Style Sheets（层叠样式表）
    - 用来控制网页的外观：颜色、大小、位置、动画等
    - 这是内联样式（写在HTML文件里），还可以用外部CSS文件
    -->
    <style>
        /* CSS重置 - 消除浏览器默认样式 */
        /* CSS注释：
           - 开始注释用斜杠星号
           - 结束注释用星号斜杠
           - CSS注释不会影响样式，只是给程序员看的说明
        */

        /* CSS选择器：
           - * = 通配符选择器，选择页面上的所有元素
           - { } = 大括号包含CSS声明块
           - 这意味着后面的样式会应用到页面上每一个HTML元素
        */
        * {
            /* CSS属性：
               - margin = 外边距，元素与其他元素之间的距离
               - : = 冒号分隔属性名和属性值
               - 0 = 属性值，表示0像素的外边距
               - ; = 分号结束一个CSS声明
               - 这让所有元素的外边距都变成0
            */
            margin: 0;

            /* padding属性：
               - padding = 内边距，元素内容与边框之间的距离
               - 0 = 0像素的内边距
               - 浏览器默认给很多元素添加padding，这里重置为0
            */
            padding: 0;

            /* box-sizing属性：
               - box-sizing = 盒模型计算方式
               - border-box = 边框盒模型
               - 这意味着元素的width和height包含padding和border
               - 让布局计算更容易，是现代CSS的最佳实践
            */
            box-sizing: border-box;
        }
        
        /* 全局样式 */
        /* CSS选择器：
        - body = 元素选择器，选择HTML的body元素
        - body是网页可见内容的容器
        */
        body {
            /* font-family属性：
            - font-family = 字体族，定义文字使用什么字体
            - 'Arial' = 首选字体Arial，用引号包围
            - sans-serif = 备选字体类型（无衬线字体）
            - 如果Arial不可用，浏览器会使用任何可用的无衬线字体
            - 逗号分隔多个字体选项
            */
            font-family: 'Arial', sans-serif;
            
            /* line-height属性：
            - line-height = 行高，文字行与行之间的距离
            - 1.6 = 相对值，表示1.6倍字体大小的行高
            - 比如字体16px，行高就是16 × 1.6 = 25.6px
            - 适当的行高让文字更易读
            */
            line-height: 1.6;
            
            /* color属性：
            - color = 文字颜色
            - #333 = 16进制颜色代码
            - # = 表示这是16进制颜色
            - 333 = 缩写形式，等于#333333
            - 333 = 深灰色（接近黑色）
            - 16进制：0-9和A-F，表示RGB三个颜色通道
            */
            color: #333;
            
            /* background-color属性：
            - background-color = 背景颜色
            - #f8f9fa = 浅灰色
            - f8f9fa分解：f8(红) f9(绿) fa(蓝)
            - 每两个字符代表一个颜色通道，值越大颜色越亮
            */
            background-color: #faf8f8;
        }
        
        /* 导航栏样式 */
        /* CSS类选择器：
        - . = 点号表示这是类选择器
        - navbar = 类名
        - 选择所有class="navbar"的HTML元素
        - 类可以重复使用，一个元素可以有多个类
        */
        .navbar {
            /* background属性（线性渐变）：
            - background = 背景属性
            - linear-gradient = 线性渐变函数
            - 135deg = 渐变角度，135度（从左上到右下）
            - #667eea = 起始颜色（蓝紫色）
            - 0% = 起始位置（渐变开始的地方）
            - #764ba2 = 结束颜色（深紫色）
            - 100% = 结束位置（渐变结束的地方）
            - deg = degrees（度数）的缩写
            */
            background: linear-gradient(135deg, #ea6666 0%, #91a24b 100%);
            
            /* padding属性（简写形式）：
            - padding = 内边距
            - 1rem = 相对单位，等于根元素字体大小的1倍
            - 0 = 左右内边距为0
            - 这是简写：上下1rem，左右0
            - rem = root em，相对于html元素的字体大小
            */
            padding: 1rem 0;
            
            /* position属性：
            - position = 定位方式
            - fixed = 固定定位
            - 元素固定在视口（浏览器窗口）的某个位置
            - 滚动页面时，固定定位的元素不会移动
            - 其他值：static（默认）、relative、absolute
            */
            position: fixed;
            
            /* top属性：
            - top = 距离顶部的距离
            - 0 = 0像素
            - 配合fixed定位，让导航栏固定在页面顶部
            - 只有当position不是static时，top才有效
            */
            top: 0;
            
            /* width属性：
            - width = 宽度
            - 100% = 百分比值，表示父元素宽度的100%
            - 让导航栏横跨整个浏览器窗口宽度
            - % = 百分比单位
            */
            width: 100%;
            
            /* z-index属性：
            - z-index = 层叠顺序（谁在上面）
            - 1000 = 层级值，数字越大越在上层
            - 确保导航栏显示在其他内容上方
            - 只对定位元素（position不是static）有效
            */
            z-index: 1000;
            
            /* box-shadow属性：
            - box-shadow = 盒阴影
            - 0 = 水平偏移量（0表示不左右偏移）
            - 2px = 垂直偏移量（向下2像素）
            - 10px = 模糊半径（数值越大阴影越模糊）
            - rgba(0,0,0,0.1) = 阴影颜色
            - rgba = Red Green Blue Alpha（红绿蓝透明度）
            - (0,0,0,0.1) = 黑色，透明度0.1（几乎透明）
            */
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        /* 类选择器：
        - nav-container = 导航容器的类名
        - 通常用于包装和约束导航内容的宽度
        */
        .nav-container {
            /* max-width属性：
            - max-width = 最大宽度
            - 1200px = 1200像素
            - 元素宽度不会超过这个值
            - 在大屏幕上限制内容宽度，提高可读性
            - px = pixel（像素）单位
            */
            max-width: 1200px;
            
            /* margin属性（水平居中）：
            - margin = 外边距
            - 0 = 上下外边距为0
            - auto = 左右外边距自动
            - auto让块级元素在父容器中水平居中
            - 这是CSS中实现水平居中的经典方法
            */
            margin: 0 auto;
            
            /* display属性：
            - display = 显示类型
            - flex = 弹性布局（Flexbox）
            - 让容器变成弹性容器，子元素变成弹性项目
            - Flexbox是现代CSS布局的核心技术
            - 可以轻松实现对齐、分布和响应式布局
            */
            display: flex;
            
            /* justify-content属性（Flexbox）：
            - justify-content = 主轴对齐方式
            - space-between = 项目间留有空隙
            - 第一个项目贴左边，最后一个项目贴右边
            - 中间项目平均分布，项目间距相等
            - 其他值：flex-start、center、flex-end、space-around
            */
            justify-content: space-between;
            
            /* align-items属性（Flexbox）：
            - align-items = 交叉轴对齐方式
            - center = 居中对齐
            - 让所有子元素在垂直方向上居中
            - 主轴是水平方向，交叉轴就是垂直方向
            - 其他值：flex-start、flex-end、stretch、baseline
            */
            align-items: center;
            
            /* padding属性：
            - 0 = 上下内边距为0
            - 2rem = 左右内边距为2rem
            - rem单位让内边距随字体大小缩放
            - 给容器左右两边留出空间
            */
            padding: 0 2rem;
        }
        
        /* 类选择器：
        - logo = logo元素的类名
        - 通常用于品牌标识、网站名称等
        */
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            text-decoration: none;
        }
        
        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .nav-menu a {
            color: rgb(255, 255, 255);
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav-menu a:hover {
            color: #ffd700;
        }
        
        /* 主要内容区域 */
        .main-content {
            margin-top: 80px;
            min-height: calc(100vh - 80px);
        }
        
        /* 英雄区域 */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 4rem 2rem;
        }
        
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            animation: fadeInUp 1s ease;
        }
        
        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            animation: fadeInUp 1s ease 0.2s both;
        }
        
        .btn {
            display: inline-block;
            background-color: #ffd700;
            color: #333;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            animation: fadeInUp 1s ease 0.4s both;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        /* 技能区域 */
        .skills {
            padding: 4rem 2rem;
            background: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #333;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .skill-card {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .skill-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }
        
        .skill-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .skill-card h3 {
            margin-bottom: 1rem;
            color: #333;
        }
        
        /* 项目区域 */
        .projects {
            padding: 4rem 2rem;
            background: #f8f9fa;
        }
        
        .project-card {
            background: white;
            margin-bottom: 2rem;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .project-card:hover {
            transform: scale(1.02);
        }
        
        .project-info {
            padding: 2rem;
        }
        
        .project-info h3 {
            margin-bottom: 1rem;
            color: #333;
        }
        
        /* 联系表单 */
        .contact {
            padding: 4rem 2rem;
            background: white;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
        }
        
        .form-container {
            max-width: 600px;
            margin: 0 auto;
        }
        
        /* 页脚 */
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 2rem;
        }
        
        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .hero h1 {
                font-size: 2rem;
            }
            
            .skills-grid {
                grid-template-columns: 1fr;
            }
        }
        
        /* 隐藏类 */
        .hidden {
            display: none;
        }
        
        /* 活跃状态 */
        .active {
            background-color: #28a745;
            color: white;
        }
    </style>
</head>

<body>
<!-- body标签：
- body = 文档主体，包含所有可见内容
- 用户在浏览器中看到的所有内容都在body里
- 与head相对，head是元数据，body是实际内容
-->

    <!-- 导航栏 -->
    <!-- HTML注释：
    - <!-- 开始注释
    - --> 结束注释
    - HTML注释不会显示在页面上，只是给开发者看的说明
    - 帮助组织和理解代码结构
    -->
    
    <!-- nav标签：
    - nav = navigation（导航）
    - HTML5语义化标签，表示导航区域
    - class="navbar" = 应用CSS类样式
    - 语义化标签让屏幕阅读器更好理解页面结构
    -->
    <nav class="navbar">
        <!-- div标签：
        - div = division（分割）
        - 通用容器元素，没有特殊含义
        - 主要用于布局和样式应用
        - class="nav-container" = 应用导航容器样式
        -->
        <div class="nav-container">
            <!-- a标签：
            - a = anchor（锚点）
            - 超链接元素，可以跳转到其他页面或页面内位置
            - href="#" = 链接地址，#表示页面顶部
            - class="logo" = 应用logo样式
            - 标签内的"张三"是链接显示的文字
            -->
            <a href="#" class="logo">张三</a>
            
            <!-- ul标签：
            - ul = unordered list（无序列表）
            - 创建项目符号列表（默认有圆点）
            - class="nav-menu" = 应用导航菜单样式
            - CSS会移除默认的项目符号
            -->
            <ul class="nav-menu">
                <!-- li标签：
                - li = list item（列表项）
                - 必须是ul或ol的直接子元素
                - 包含一个导航链接
                
                a标签属性：
                - href="#home" = 链接到页面内id为"home"的元素
                - onclick = JavaScript事件处理
                - scrollToSection('home') = 点击时执行JavaScript函数
                - 'home' = 传给函数的参数（字符串）
                -->
                <li><a href="#home" onclick="scrollToSection('home')">首页</a></li>
                <li><a href="#skills" onclick="scrollToSection('skills')">技能</a></li>
                <li><a href="#projects" onclick="scrollToSection('projects')">项目</a></li>
                <li><a href="#contact" onclick="scrollToSection('contact')">联系</a></li>
            </ul>
        </div>
    </nav>

    <!-- 主要内容 -->
    <!-- 主要内容容器：
    - 包含除导航栏和页脚外的所有内容
    - CSS设置了上外边距来避开固定导航栏
    -->
    <div class="main-content">
        <!-- 英雄区域 -->
        <!-- section标签：
        - section = HTML5语义化标签
        - 表示文档的一个章节或区域
        - id="home" = 唯一标识符，JavaScript和CSS可以通过它选择元素
        - 导航链接会跳转到这个id
        - class="hero" = 应用英雄区域样式
        -->
        <section id="home" class="hero">
            <!-- 容器div：
            - 限制内容宽度并居中
            - 复用container类样式
            -->
            <div class="container">
                <!-- h1标签：
                - h1 = heading level 1（1级标题）
                - 页面最重要的标题，一个页面通常只有一个h1
                - id="heroTitle" = JavaScript会用这个id来改变标题文字
                - SEO重要：搜索引擎认为h1是页面最重要的内容
                -->
                <h1 id="heroTitle">你好，我是张三</h1>
                
                <!-- p标签：
                - p = paragraph（段落）
                - 包含一段文本内容
                - id="heroSubtitle" = 副标题的标识符
                - | = 竖线字符，用作分隔符（不是HTML语法）
                -->
                <p id="heroSubtitle">前端开发工程师 | 专注于用户体验</p>
                
                <!-- 行动按钮：
                - href="#contact" = 链接到联系区域
                - class="btn" = 应用按钮样式
                - onclick = 点击时执行平滑滚动
                - 这是典型的CTA（Call To Action）按钮
                -->
                <a href="#contact" class="btn" onclick="scrollToSection('contact')">联系我</a>
            </div>
        </section>

        <!-- 技能区域 -->
        <section id="skills" class="skills">
            <div class="container">
                <!-- h2标签：
                - h2 = heading level 2（2级标题）
                - 比h1级别低，用于章节标题
                - class="section-title" = 章节标题样式
                - HTML标题有层级：h1 > h2 > h3 > h4 > h5 > h6
                -->
                <h2 class="section-title">我的技能</h2>
                
                <!-- 技能网格容器：
                - CSS Grid布局的容器
                - 子元素会自动排列成网格
                -->
                <div class="skills-grid">
                    <!-- 技能卡片：
                    - class="skill-card" = 卡片样式
                    - onclick="showSkillDetail('HTML')" = 点击显示HTML详情
                    - 'HTML' = 传给JavaScript函数的参数
                    -->
                    <div class="skill-card" onclick="showSkillDetail('HTML')">
                        <!-- 技能图标：
                        - class="skill-icon" = 图标样式
                        - 🌐 = Unicode emoji字符（地球图标）
                        - emoji是Unicode字符，可以直接在HTML中使用
                        -->
                        <div class="skill-icon">🌐</div>
                        
                        <!-- h3标签：
                        - h3 = 3级标题
                        - 技能名称，比h2级别低
                        -->
                        <h3>HTML5</h3>
                        
                        <!-- 技能描述：
                        - 简短描述技能特点
                        -->
                        <p>语义化标签，响应式布局</p>
                    </div>
                    
                    <div class="skill-card" onclick="showSkillDetail('CSS')">
                        <div class="skill-icon">🎨</div>
                        <h3>CSS3</h3>
                        <p>Flexbox, Grid, 动画效果</p>
                    </div>
                    
                    <div class="skill-card" onclick="showSkillDetail('JavaScript')">
                        <div class="skill-icon">⚡</div>
                        <h3>JavaScript</h3>
                        <p>ES6+, DOM操作, API调用</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 项目区域 -->
        <section id="projects" class="projects">
            <div class="container">
                <h2 class="section-title">我的项目</h2>
                
                <div class="project-card">
                    <div class="project-info">
                        <h3>个人作品集网站</h3>
                        <p>使用HTML、CSS、JavaScript构建的响应式个人网站</p>
                        
                        <!-- button标签：
                        - button = 按钮元素
                        - onclick = 点击事件处理
                        - toggleProjectDetails(this) = 调用切换详情函数
                        - this = JavaScript关键字，指代当前按钮元素
                        - class="btn" = 复用按钮样式
                        -->
                        <button onclick="toggleProjectDetails(this)" class="btn">查看详情</button>
                        
                        <!-- 项目详情容器：
                        - class="project-details hidden" = 两个类
                        - project-details = 详情样式
                        - hidden = 隐藏状态（CSS display: none）
                        - JavaScript会切换hidden类来显示/隐藏
                        -->
                        <div class="project-details hidden">
                            <p>技术栈：HTML5, CSS3, JavaScript</p>
                            <p>特点：响应式设计，平滑滚动，动画效果</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系区域 -->
        <section id="contact" class="contact">
            <div class="container">
                <h2 class="section-title">联系我</h2>
                
                <div class="form-container">
                    <!-- form标签：
                    - form = 表单元素
                    - 包含用户输入的控件
                    - id="contactForm" = 表单的唯一标识
                    - onsubmit = 表单提交事件处理
                    - handleFormSubmit(event) = 处理提交的函数
                    - event = 事件对象，包含提交事件信息
                    -->
                    <form id="contactForm" onsubmit="handleFormSubmit(event)">
                        <div class="form-group">
                            <!-- label标签：
                            - label = 标签元素
                            - 为表单控件提供说明文字
                            - for="name" = 关联到id为"name"的输入控件
                            - 点击标签会自动聚焦到对应输入框
                            - 提高可访问性（accessibility）
                            -->
                            <label for="name">姓名</label>
                            
                            <!-- input标签：
                            - input = 输入控件
                            - type="text" = 文本输入类型
                            - id="name" = 唯一标识符，与label的for属性对应
                            - name="name" = 表单字段名，提交时的键名
                            - required = HTML5属性，表示必填字段
                            - 浏览器会自动验证required字段
                            -->
                            <input type="text" id="name" name="name" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">邮箱</label>
                            <!-- email类型输入：
                            - type="email" = 邮箱输入类型
                            - 浏览器会验证邮箱格式
                            - 手机上会显示邮箱专用键盘
                            - 提供更好的用户体验
                            -->
                            <input type="email" id="email" name="email" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="message">留言</label>
                            <!-- textarea标签：
                            - textarea = 多行文本输入区域
                            - 不像input是自闭合标签，textarea有开始和结束标签
                            - rows="5" = 显示5行高度
                            - 用户可以调整大小（默认行为）
                            - 适合长文本输入
                            -->
                            <textarea id="message" name="message" rows="5" required></textarea>
                        </div>
                        
                        <!-- 提交按钮：
                        - type="submit" = 提交类型按钮
                        - 点击会触发表单的onsubmit事件
                        - 在表单内的submit按钮会自动提交表单
                        -->
                        <button type="submit" class="btn">发送消息</button>
                    </form>
                </div>
            </div>
        </section>
    </div>

    <!-- 页脚 -->
    <!-- footer标签：
    - footer = HTML5语义化标签
    - 表示页脚区域
    - 通常包含版权信息、链接等
    -->
    <footer class="footer">
        <!-- 版权信息：
        - &copy; = HTML实体，表示版权符号 ©
        - HTML实体用于显示特殊字符
        - 其他常用实体：&amp;(&), &lt;(<), &gt;(>), &nbsp;(空格)
        -->
        <p>&copy; 2025 张三. 保留所有权利.</p>
    </footer>

    <!-- script标签：
    - script = 脚本标签
    - 包含JavaScript代码
    - 也可以通过src属性引用外部JS文件
    - 放在body底部可以确保DOM加载完成后再执行
    -->
    <script>
        // JavaScript 代码开始
        // JavaScript注释：
        // - // = 单行注释
        // - 只注释当前行 // 后面的内容
        // - 还有多行注释：/* 注释内容 */
        
        // 全局变量
        // 变量声明：
        // - let = ES6变量声明关键字
        // - currentTheme = 变量名（驼峰命名法）
        // - 'light' = 字符串值，用单引号包围
        // - ; = JavaScript语句结束分号
        // - let声明的变量有块级作用域
        let currentTheme = 'light';
        
        // null值：
        // - null = JavaScript特殊值，表示"空"
        // - 与undefined不同，null是主动赋予的空值
        // - 这个变量用于存储动画ID
        let animationId = null;
        
        // 页面加载完成后执行
        // 事件监听器：
        // - document = 文档对象，代表整个HTML页面
        // - addEventListener = 添加事件监听器的方法
        // - 'DOMContentLoaded' = 事件类型，DOM加载完成时触发
        // - function() = 匿名函数，事件处理器
        // - {} = 函数体，包含要执行的代码
        document.addEventListener('DOMContentLoaded', function() {
            // 控制台输出：
            // - console = 浏览器控制台对象
            // - log() = 输出方法
            // - '页面加载完成！' = 要输出的字符串
            // - 开发者可以在浏览器F12控制台看到这个消息
            console.log('页面加载完成！');
            
            // 函数调用：
            // - initializeWebsite = 函数名
            // - () = 函数调用操作符，无参数
            // - 调用下面定义的初始化函数
            initializeWebsite();
        });
        
        // 初始化网站
        // 函数声明：
        // - function = 函数声明关键字
        // - initializeWebsite = 函数名
        // - () = 参数列表，这里没有参数
        // - {} = 函数体
        function initializeWebsite() {
            // 添加欢迎消息
            // 定时器函数：
            // - setTimeout = 延时执行函数
            // - function() = 要延时执行的匿名函数
            // - 第二个参数是延时时间（毫秒）
            setTimeout(function() {
                // 弹窗函数：
                // - alert() = 浏览器弹出警告框
                // - 显示消息并等待用户点击确定
                // - 会阻塞页面执行直到用户响应
                alert('欢迎访问我的个人网站！');
            }, 1000); // 延时时间：1000 = 1000毫秒 = 1秒
            
            // 初始化导航
            initializeNavigation();
        }
        
        // 初始化导航功能
        function initializeNavigation() {
            // 元素选择：
            // - const = ES6常量声明关键字
            // - navLinks = 常量名
            // - document.querySelectorAll() = 选择所有匹配的元素
            // - '.nav-menu a' = CSS选择器，选择nav-menu类内的所有a元素
            // - 返回NodeList（类似数组的对象）
            const navLinks = document.querySelectorAll('.nav-menu a');
            
            // 数组遍历：
            // - forEach() = 数组/NodeList遍历方法
            // - function(link) = 回调函数，每个元素会调用一次
            // - link = 当前遍历到的元素（参数名可以自定义）
            navLinks.forEach(function(link) {
                // 给每个链接添加点击事件：
                // - addEventListener() = 添加事件监听器方法
                // - 'click' = 点击事件类型
                // - function(e) = 事件处理函数
                // - e = event的缩写，事件对象参数
                link.addEventListener('click', function(e) {
                    // 阻止默认行为：
                    // - e.preventDefault() = 阻止事件的默认行为
                    // - 对于链接，默认行为是页面跳转
                    // - 这里阻止跳转，改用JavaScript控制
                    e.preventDefault();
                    
                    // 移除所有active类
                    // - forEach = 遍历所有导航链接
                    // - l => = 箭头函数语法（ES6）
                    // - l.classList = 元素的类列表对象
                    // - remove('active') = 移除active类
                    // - 确保同时只有一个链接是活跃的
                    navLinks.forEach(l => l.classList.remove('active'));
                    
                    // 给当前链接添加active类
                    // - this = 指向当前被点击的链接元素
                    // - classList.add() = 添加CSS类
                    // - 'active' = 要添加的类名
                    this.classList.add('active');
                });
            });
        }
        
        // 平滑滚动到指定区域
        function scrollToSection(sectionId) {
            const section = document.getElementById(sectionId);
            
            if (section) {
                section.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }
        
        // 显示技能详情
        function showSkillDetail(skillName) {
            const messages = {
                'HTML': 'HTML是网页的骨架，定义内容结构',
                'CSS': 'CSS是网页的外观，控制样式和布局',
                'JavaScript': 'JavaScript是网页的大脑，实现交互功能'
            };
            
            alert(`${skillName}: ${messages[skillName]}`);
        }
        
        // 切换项目详情显示
        function toggleProjectDetails(button) {
            const details = button.nextElementSibling;
            
            if (details.classList.contains('hidden')) {
                details.classList.remove('hidden');
                button.textContent = '隐藏详情';
            } else {
                details.classList.add('hidden');
                button.textContent = '查看详情';
            }
        }
        
        // 处理表单提交
        function handleFormSubmit(event) {
            event.preventDefault();
            
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const message = document.getElementById('message').value;
            
            if (!name || !email || !message) {
                alert('请填写所有必填字段！');
                return false;
            }
            
            alert(`谢谢 ${name}！您的消息已发送。我会尽快回复您的邮箱 ${email}`);
            
            document.getElementById('contactForm').reset();
            
            return false;
        }
        
        // 动态改变标题文字
        function changeHeroTitle() {
            const titles = [
                '你好，我是张三',
                'Hello, I am Zhang San',
                '欢迎来到我的世界',
                '让我们一起创造未来'
            ];
            
            let currentIndex = 0;
            const titleElement = document.getElementById('heroTitle');
            
            setInterval(function() {
                currentIndex = (currentIndex + 1) % titles.length;
                titleElement.textContent = titles[currentIndex];
            }, 3000);
        }
        
        // 启动标题动画
        setTimeout(changeHeroTitle, 2000);
        
        // 监听滚动事件
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            
            if (window.scrollY > 100) {
                navbar.style.backgroundColor = 'rgba(102, 126, 234, 0.95)';
            } else {
                navbar.style.backgroundColor = '';
            }
        });
        
        // 键盘事件监听
        document.addEventListener('keydown', function(event) {
            if (event.key === 'Escape') {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }
        });
        
        // 鼠标点击特效
        document.addEventListener('click', function(event) {
            createClickEffect(event.clientX, event.clientY);
        });
        
        function createClickEffect(x, y) {
            const effect = document.createElement('div');
            effect.style.position = 'fixed';
            effect.style.left = x + 'px';
            effect.style.top = y + 'px';
            effect.style.width = '10px';
            effect.style.height = '10px';
            effect.style.background = '#ffd700';
            effect.style.borderRadius = '50%';
            effect.style.pointerEvents = 'none';
            effect.style.animation = 'clickEffect 0.6s ease-out forwards';
            
            document.body.appendChild(effect);
            
            setTimeout(function() {
                document.body.removeChild(effect);
            }, 600);
        }
        
        // 添加点击特效的CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes clickEffect {
                0% {
                    transform: scale(1);
                    opacity: 1;
                }
                100% {
                    transform: scale(5);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
        
        console.log('JavaScript初始化完成！');
    </script>
</body>
</html>