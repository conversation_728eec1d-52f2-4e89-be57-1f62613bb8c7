<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="张三的个人作品集 - 前端开发工程师">
    <title>张三 - 前端开发工程师</title>
    
    <style>
        /* CSS重置 - 消除浏览器默认样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        /* 全局样式 */
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        /* 导航栏样式 */
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 1rem 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 1000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .nav-container {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 2rem;
        }
        
        .logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            text-decoration: none;
        }
        
        .nav-menu {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .nav-menu a {
            color: white;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }
        
        .nav-menu a:hover {
            color: #ffd700;
        }
        
        /* 主要内容区域 */
        .main-content {
            margin-top: 80px;
            min-height: calc(100vh - 80px);
        }
        
        /* 英雄区域 */
        .hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
            padding: 4rem 2rem;
        }
        
        .hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
            animation: fadeInUp 1s ease;
        }
        
        .hero p {
            font-size: 1.2rem;
            margin-bottom: 2rem;
            animation: fadeInUp 1s ease 0.2s both;
        }
        
        .btn {
            display: inline-block;
            background-color: #ffd700;
            color: #333;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            animation: fadeInUp 1s ease 0.4s both;
        }
        
        .btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }
        
        /* 技能区域 */
        .skills {
            padding: 4rem 2rem;
            background: white;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .section-title {
            text-align: center;
            font-size: 2.5rem;
            margin-bottom: 3rem;
            color: #333;
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .skill-card {
            background: #f8f9fa;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 1px solid #e9ecef;
        }
        
        .skill-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0,0,0,0.1);
        }
        
        .skill-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .skill-card h3 {
            margin-bottom: 1rem;
            color: #333;
        }
        
        /* 项目区域 */
        .projects {
            padding: 4rem 2rem;
            background: #f8f9fa;
        }
        
        .project-card {
            background: white;
            margin-bottom: 2rem;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .project-card:hover {
            transform: scale(1.02);
        }
        
        .project-info {
            padding: 2rem;
        }
        
        .project-info h3 {
            margin-bottom: 1rem;
            color: #333;
        }
        
        /* 联系表单 */
        .contact {
            padding: 4rem 2rem;
            background: white;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 1rem;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
        }
        
        .form-container {
            max-width: 600px;
            margin: 0 auto;
        }
        
        /* 页脚 */
        .footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 2rem;
        }
        
        /* 动画效果 */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }
            
            .hero h1 {
                font-size: 2rem;
            }
            
            .skills-grid {
                grid-template-columns: 1fr;
            }
        }
        
        /* 隐藏类 */
        .hidden {
            display: none;
        }
        
        /* 活跃状态 */
        .active {
            background-color: #28a745;
            color: white;
        }
    </style>
</head>

<body>
    <!-- 导航栏 -->
    <nav class="navbar">
        <div class="nav-container">
            <a href="#" class="logo">张三</a>
            <ul class="nav-menu">
                <li><a href="#home" onclick="scrollToSection('home')">首页</a></li>
                <li><a href="#skills" onclick="scrollToSection('skills')">技能</a></li>
                <li><a href="#projects" onclick="scrollToSection('projects')">项目</a></li>
                <li><a href="#contact" onclick="scrollToSection('contact')">联系</a></li>
            </ul>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="main-content">
        <!-- 英雄区域 -->
        <section id="home" class="hero">
            <div class="container">
                <h1 id="heroTitle">你好，我是张三</h1>
                <p id="heroSubtitle">前端开发工程师 | 专注于用户体验</p>
                <a href="#contact" class="btn" onclick="scrollToSection('contact')">联系我</a>
            </div>
        </section>

        <!-- 技能区域 -->
        <section id="skills" class="skills">
            <div class="container">
                <h2 class="section-title">我的技能</h2>
                <div class="skills-grid">
                    <div class="skill-card" onclick="showSkillDetail('HTML')">
                        <div class="skill-icon">🌐</div>
                        <h3>HTML5</h3>
                        <p>语义化标签，响应式布局</p>
                    </div>
                    <div class="skill-card" onclick="showSkillDetail('CSS')">
                        <div class="skill-icon">🎨</div>
                        <h3>CSS3</h3>
                        <p>Flexbox, Grid, 动画效果</p>
                    </div>
                    <div class="skill-card" onclick="showSkillDetail('JavaScript')">
                        <div class="skill-icon">⚡</div>
                        <h3>JavaScript</h3>
                        <p>ES6+, DOM操作, API调用</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- 项目区域 -->
        <section id="projects" class="projects">
            <div class="container">
                <h2 class="section-title">我的项目</h2>
                <div class="project-card">
                    <div class="project-info">
                        <h3>个人作品集网站</h3>
                        <p>使用HTML、CSS、JavaScript构建的响应式个人网站</p>
                        <button onclick="toggleProjectDetails(this)" class="btn">查看详情</button>
                        <div class="project-details hidden">
                            <p>技术栈：HTML5, CSS3, JavaScript</p>
                            <p>特点：响应式设计，平滑滚动，动画效果</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 联系区域 -->
        <section id="contact" class="contact">
            <div class="container">
                <h2 class="section-title">联系我</h2>
                <div class="form-container">
                    <form id="contactForm" onsubmit="handleFormSubmit(event)">
                        <div class="form-group">
                            <label for="name">姓名</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="email">邮箱</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="message">留言</label>
                            <textarea id="message" name="message" rows="5" required></textarea>
                        </div>
                        <button type="submit" class="btn">发送消息</button>
                    </form>
                </div>
            </div>
        </section>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <p>&copy; 2025 张三. 保留所有权利.</p>
    </footer>

    <script>
        // JavaScript 代码开始
        
        // 全局变量
        let currentTheme = 'light';
        let animationId = null;
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成！');
            initializeWebsite();
        });
        
        // 初始化网站
        function initializeWebsite() {
            // 添加欢迎消息
            setTimeout(function() {
                alert('欢迎访问我的个人网站！');
            }, 1000);
            
            // 初始化导航
            initializeNavigation();
        }
        
        // 初始化导航功能
        function initializeNavigation() {
            const navLinks = document.querySelectorAll('.nav-menu a');
            
            navLinks.forEach(function(link) {
                link.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // 移除所有active类
                    navLinks.forEach(l => l.classList.remove('active'));
                    
                    // 给当前链接添加active类
                    this.classList.add('active');
                });
            });
        }
        
        // 平滑滚动到指定区域
        function scrollToSection(sectionId) {
            const section = document.getElementById(sectionId);
            
            if (section) {
                section.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        }
        
        // 显示技能详情
        function showSkillDetail(skillName) {
            const messages = {
                'HTML': 'HTML是网页的骨架，定义内容结构',
                'CSS': 'CSS是网页的外观，控制样式和布局',
                'JavaScript': 'JavaScript是网页的大脑，实现交互功能'
            };
            
            alert(`${skillName}: ${messages[skillName]}`);
        }
        
        // 切换项目详情显示
        function toggleProjectDetails(button) {
            const details = button.nextElementSibling;
            
            if (details.classList.contains('hidden')) {
                details.classList.remove('hidden');
                button.textContent = '隐藏详情';
            } else {
                details.classList.add('hidden');
                button.textContent = '查看详情';
            }
        }
        
        // 处理表单提交
        function handleFormSubmit(event) {
            // 阻止表单默认提交行为
            event.preventDefault();
            
            // 获取表单数据
            const name = document.getElementById('name').value;
            const email = document.getElementById('email').value;
            const message = document.getElementById('message').value;
            
            // 验证数据
            if (!name || !email || !message) {
                alert('请填写所有必填字段！');
                return false;
            }
            
            // 模拟发送成功
            alert(`谢谢 ${name}！您的消息已发送。我会尽快回复您的邮箱 ${email}`);
            
            // 清空表单
            document.getElementById('contactForm').reset();
            
            return false;
        }
        
        // 动态改变标题文字
        function changeHeroTitle() {
            const titles = [
                '你好，我是张三',
                'Hello, I am Zhang San',
                '欢迎来到我的世界',
                '让我们一起创造未来'
            ];
            
            let currentIndex = 0;
            const titleElement = document.getElementById('heroTitle');
            
            setInterval(function() {
                currentIndex = (currentIndex + 1) % titles.length;
                titleElement.textContent = titles[currentIndex];
            }, 3000);
        }
        
        // 启动标题动画
        setTimeout(changeHeroTitle, 2000);
        
        // 监听滚动事件
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            
            if (window.scrollY > 100) {
                navbar.style.backgroundColor = 'rgba(102, 126, 234, 0.95)';
            } else {
                navbar.style.backgroundColor = '';
            }
        });
        
        // 键盘事件监听
        document.addEventListener('keydown', function(event) {
            // 按ESC键返回顶部
            if (event.key === 'Escape') {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            }
        });
        
        // 鼠标点击特效
        document.addEventListener('click', function(event) {
            createClickEffect(event.clientX, event.clientY);
        });
        
        function createClickEffect(x, y) {
            const effect = document.createElement('div');
            effect.style.position = 'fixed';
            effect.style.left = x + 'px';
            effect.style.top = y + 'px';
            effect.style.width = '10px';
            effect.style.height = '10px';
            effect.style.background = '#ffd700';
            effect.style.borderRadius = '50%';
            effect.style.pointerEvents = 'none';
            effect.style.animation = 'clickEffect 0.6s ease-out forwards';
            
            document.body.appendChild(effect);
            
            setTimeout(function() {
                document.body.removeChild(effect);
            }, 600);
        }
        
        // 添加点击特效的CSS动画
        const style = document.createElement('style');
        style.textContent = `
            @keyframes clickEffect {
                0% {
                    transform: scale(1);
                    opacity: 1;
                }
                100% {
                    transform: scale(5);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
        
        console.log('JavaScript初始化完成！');
    </script>
</body>
</html>